using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace Soup
{
    /// <summary>
    /// Core property discovery functionality shared between runtime and editor.
    /// Provides efficient, cached property discovery for ValueStore types.
    /// </summary>
    public static class PropertyDiscoveryCore
    {
        /// <summary>
        /// Basic property information for runtime use
        /// </summary>
        public readonly struct PropertyInfo
        {
            public readonly string Name;
            public readonly Type Type;
            public readonly bool IsProperty;
            public readonly bool IsReadable;

            public PropertyInfo(string name, Type type, bool isProperty, bool isReadable = true)
            {
                Name = name;
                Type = type;
                IsProperty = isProperty;
                IsReadable = isReadable;
            }

            public bool IsDirectBinding => string.IsNullOrEmpty(Name);
        }

        // Cache for discovered properties to improve performance
        private static readonly Dictionary<Type, List<PropertyInfo>> _propertyCache = new();

        /// <summary>
        /// Discovers all public properties and fields on the given type with caching
        /// </summary>
        /// <param name="valueType">The type to discover properties on</param>
        /// <returns>Cached list of discoverable properties and fields</returns>
        public static List<PropertyInfo> DiscoverProperties(Type valueType)
        {
            if (valueType == null)
                return new List<PropertyInfo>();

            // Check cache first
            if (_propertyCache.TryGetValue(valueType, out var cachedProperties))
                return cachedProperties;

            var properties = new List<PropertyInfo>
            {
                // Direct binding is always first (index 0)
                new("", valueType, true)
            };

            // Discover public properties
            var typeProperties = valueType.GetProperties(BindingFlags.Public | BindingFlags.Instance);
            foreach (var prop in typeProperties.Where(p => p.CanRead))
            {
                properties.Add(new PropertyInfo(prop.Name, prop.PropertyType, true));
            }

            // Discover public fields
            var typeFields = valueType.GetFields(BindingFlags.Public | BindingFlags.Instance);
            foreach (var field in typeFields)
            {
                properties.Add(new PropertyInfo(field.Name, field.FieldType, false));
            }

            // Sort by name (direct binding stays first)
            var sortedProperties = properties.OrderBy(p => p.IsDirectBinding ? "" : p.Name).ToList();
            
            // Cache the result
            _propertyCache[valueType] = sortedProperties;
            
            return sortedProperties;
        }

        /// <summary>
        /// Gets the property name by index safely
        /// </summary>
        /// <param name="properties">List of discovered properties</param>
        /// <param name="index">Index to get name for</param>
        /// <returns>Property name, or empty string for direct binding or invalid index</returns>
        public static string GetPropertyNameByIndex(List<PropertyInfo> properties, int index)
        {
            return IsValidIndex(properties, index) ? properties[index].Name : "";
        }

        /// <summary>
        /// Gets the property info by index safely
        /// </summary>
        /// <param name="properties">List of discovered properties</param>
        /// <param name="index">Index to get property for</param>
        /// <returns>Property info, or default if invalid index</returns>
        public static PropertyInfo GetPropertyByIndex(List<PropertyInfo> properties, int index)
        {
            return IsValidIndex(properties, index) ? properties[index] : default;
        }

        /// <summary>
        /// Validates if the given index is within bounds
        /// </summary>
        /// <param name="properties">List of properties</param>
        /// <param name="index">Index to validate</param>
        /// <returns>True if index is valid</returns>
        public static bool IsValidIndex(List<PropertyInfo> properties, int index)
        {
            return properties != null && index >= 0 && index < properties.Count;
        }

        /// <summary>
        /// Clears the property discovery cache (useful for testing or memory management)
        /// </summary>
        public static void ClearCache()
        {
            _propertyCache.Clear();
        }
    }
}
